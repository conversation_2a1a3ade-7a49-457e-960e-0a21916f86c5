using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Composite;

/// <summary>
/// Unit tests for UploadWideValidationSpecification.
/// Tests the composite specification that validates upload-wide business rules.
/// </summary>
public class UploadWideValidationSpecificationTests
{
    #region Test Setup

    private readonly Mock<UploadMustHaveUniqueEmailsSpecification> _mockUploadUniqueEmailsSpec;
    private readonly Mock<UploadMustHaveUniqueIdentificationSpecification> _mockUploadUniqueIdSpec;
    private readonly Mock<UploadMustHaveUniqueMemberIdsSpecification> _mockUploadUniqueMemberIdsSpec;
    private readonly Mock<DependentAndEmployeeMustBeOnSamePlanSpecification> _mockDependentPlanSpec;
    private readonly Mock<IUploadValidationOrchestrator> _mockValidationOrchestrator;
    private readonly Mock<ILogger<UploadWideValidationSpecification>> _mockLogger;
    private readonly UploadWideValidationSpecification _specification;

    public UploadWideValidationSpecificationTests()
    {
        // Create mocks for the constituent specifications with their required logger dependencies
        _mockUploadUniqueEmailsSpec = new Mock<UploadMustHaveUniqueEmailsSpecification>(
            Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        _mockUploadUniqueIdSpec = new Mock<UploadMustHaveUniqueIdentificationSpecification>(
            Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        _mockUploadUniqueMemberIdsSpec = new Mock<UploadMustHaveUniqueMemberIdsSpecification>(
            Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        _mockDependentPlanSpec = new Mock<DependentAndEmployeeMustBeOnSamePlanSpecification>(
            Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        _mockValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        _mockLogger = new Mock<ILogger<UploadWideValidationSpecification>>();

        _specification = new UploadWideValidationSpecification(
            _mockUploadUniqueEmailsSpec.Object,
            _mockUploadUniqueIdSpec.Object,
            _mockUploadUniqueMemberIdsSpec.Object,
            _mockDependentPlanSpec.Object,
            _mockValidationOrchestrator.Object,
            _mockLogger.Object);
    }

    #endregion

    #region Business Rule Properties Tests

    [Fact]
    public void BusinessRuleName_ShouldReturnExpectedValue()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        businessRuleName.Should().Be("Upload-Wide Validation");
    }

    [Fact]
    public void Description_ShouldReturnExpectedValue()
    {
        // Act
        string description = _specification.Description;

        // Assert
        description.Should().Be("Validates business rules that apply across the entire upload dataset");
    }

    #endregion

    #region ValidateBatchAsync Tests

    [Fact]
    public async Task ValidateBatchAsync_WithValidUpload_ShouldReturnSuccessResult()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 10);
        var emptyErrors = new Dictionary<int, List<ValidationError>>();

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(10);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithUploadErrors_ShouldReturnErrorResult()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 10);
        var uploadErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("DUPLICATE_EMAIL", "email")],
            [2] = [CreateValidationError("DUPLICATE_ID", "hkid")],
            [5] = [CreateValidationError("DUPLICATE_MEMBER_ID", "memberId")]
        };

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(7); // 10 - 3 invalid
        result.InvalidCount.Should().Be(3);
        result.RowErrors.Should().HaveCount(3);
        result.RowErrors.Should().ContainKey(0);
        result.RowErrors.Should().ContainKey(2);
        result.RowErrors.Should().ContainKey(5);
    }

    [Fact]
    public async Task ValidateBatchAsync_WithEmptyUpload_ShouldReturnSuccessWithZeroCount()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 0);
        var emptyErrors = new Dictionary<int, List<ValidationError>>();

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithAllMembersInvalid_ShouldReturnAllErrorsResult()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 3);
        var allErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("ERROR_1", "field1")],
            [1] = [CreateValidationError("ERROR_2", "field2")],
            [2] = [CreateValidationError("ERROR_3", "field3")]
        };

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(allErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(3);
        result.RowErrors.Should().HaveCount(3);
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    public async Task ValidateBatchAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _specification.EvaluateBusinessRulesAsync(null!));

    [Fact]
    public async Task ValidateBatchAsync_WhenOrchestratorThrowsException_ShouldLogAndRethrow()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 5);
        var expectedException = new InvalidOperationException("Orchestrator failed");

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException actualException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _specification.EvaluateBusinessRulesAsync(context));

        actualException.Should().Be(expectedException);

        // Verify error logging occurred
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error in upload-wide validation")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region Verification Tests

    [Fact]
    public async Task ValidateBatchAsync_ShouldCallOrchestratorWithCorrectParameters()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 5);
        var emptyErrors = new Dictionary<int, List<ValidationError>>();

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyErrors);

        // Act
        await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        _mockValidationOrchestrator.Verify(
            x => x.ExecuteUploadValidationsAsync(
                context,
                It.Is<UploadValidationSpecs>(specs =>
                    specs.UniqueEmailsSpec != null &&
                    specs.UniqueIdSpec != null &&
                    specs.UniqueMemberIdsSpec != null &&
                    specs.DependentPlanSpec != null),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ValidateBatchAsync_ShouldLogValidationStartAndCompletion()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 5);
        var emptyErrors = new Dictionary<int, List<ValidationError>>();

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyErrors);

        // Act
        await _specification.EvaluateBusinessRulesAsync(context);

        // Assert - Verify start logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Starting upload-wide validation")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        // Assert - Verify completion logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Upload-wide validation completed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }



    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task ValidateBatchAsync_WithLargeUpload_ShouldHandleCorrectly()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 1000);
        var someErrors = new Dictionary<int, List<ValidationError>>
        {
            [100] = [CreateValidationError("DUPLICATE_EMAIL", "email")],
            [500] = [CreateValidationError("DUPLICATE_ID", "hkid")]
        };

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(someErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(998); // 1000 - 2 invalid
        result.InvalidCount.Should().Be(2);
        result.RowErrors.Should().HaveCount(2);
    }

    [Fact]
    public async Task ValidateBatchAsync_WithCancellationToken_ShouldPassTokenToOrchestrator()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 5);
        var cancellationToken = new CancellationToken();
        var emptyErrors = new Dictionary<int, List<ValidationError>>();

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                cancellationToken))
            .ReturnsAsync(emptyErrors);

        // Act
        await _specification.EvaluateBusinessRulesAsync(context, cancellationToken);

        // Assert
        _mockValidationOrchestrator.Verify(
            x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task ValidateBatchAsync_WithMultipleErrorsPerRow_ShouldPreserveAllErrors()
    {
        // Arrange
        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 2);
        var multipleErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [
                CreateValidationError("DUPLICATE_EMAIL", "email"),
                CreateValidationError("DUPLICATE_ID", "hkid"),
                CreateValidationError("DUPLICATE_MEMBER_ID", "memberId")
            ],
            [1] = [
                CreateValidationError("INVALID_PLAN", "planId")
            ]
        };

        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(multipleErrors);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(2);
        result.RowErrors[0].Should().HaveCount(3);
        result.RowErrors[1].Should().HaveCount(1);
        result.RowErrors[0].Should().Contain(e => e.Code == "DUPLICATE_EMAIL");
        result.RowErrors[0].Should().Contain(e => e.Code == "DUPLICATE_ID");
        result.RowErrors[0].Should().Contain(e => e.Code == "DUPLICATE_MEMBER_ID");
        result.RowErrors[1].Should().Contain(e => e.Code == "INVALID_PLAN");
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task ValidateBatchAsync_WithRealOrchestrator_ShouldWorkCorrectly()
    {
        // Arrange - Create specification with real orchestrator for integration testing
        var realOrchestrator = new UploadValidationOrchestrator(Mock.Of<ILogger<UploadValidationOrchestrator>>());
        var realSpecification = new UploadWideValidationSpecification(
            _mockUploadUniqueEmailsSpec.Object,
            _mockUploadUniqueIdSpec.Object,
            _mockUploadUniqueMemberIdsSpec.Object,
            _mockDependentPlanSpec.Object,
            realOrchestrator,
            _mockLogger.Object);

        UploadWideValidationContext context = CreateValidUploadWideValidationContext(memberCount: 3);

        // Setup mocks to return specific errors for integration test
        _mockUploadUniqueEmailsSpec.Setup(x => x.ValidateWithRowIndexedErrors(It.IsAny<UploadUniquenessValidationContext>()))
            .Returns(new Dictionary<int, List<ValidationError>>
            {
                [0] = [CreateValidationError("DUPLICATE_EMAIL", "email")]
            });
        _mockUploadUniqueIdSpec.Setup(x => x.ValidateWithRowIndexedErrors(It.IsAny<UploadUniquenessValidationContext>()))
            .Returns([]);
        _mockUploadUniqueMemberIdsSpec.Setup(x => x.ValidateWithRowIndexedErrors(It.IsAny<UploadUniquenessValidationContext>()))
            .Returns([]);
        _mockDependentPlanSpec.Setup(x => x.ValidateWithRowIndexedErrors(It.IsAny<DependentPlanValidationContext>()))
            .Returns([]);

        // Act
        BatchValidationResult result = await realSpecification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(2);
        result.InvalidCount.Should().Be(1);
        result.RowErrors.Should().HaveCount(1);
        result.RowErrors[0].Should().Contain(e => e.Code == "DUPLICATE_EMAIL");
    }

    #endregion

    #region Helper Methods

    private static UploadWideValidationContext CreateValidUploadWideValidationContext(int memberCount)
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            memberCount,
            null);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        PoliciesV3.Domain.CustomFields.PolicyMemberFieldsSchema schema = CreateTestSchema();
        MembersUploadFields membersFields = memberCount > 0
            ? MemberUploadTestDataBuilder.Create().WithMemberCount(memberCount).BuildMembersUploadFields()
            : MembersUploadFields.Empty();

        return UploadWideValidationContext.Create(
            upload, policy, membersFields, schema, resolvedData);
    }

    private static ResolvedValidationData CreateTestResolvedValidationData() => new()
    {
        UseTheSamePlanForEmployeeAndDependents = false,
        OnlyApplyForSmeProducts = false,
        AllowMembersFromOtherContractHolders = false,
        AvailablePlans = new HashSet<string> { "PLAN-001", "PLAN-002", "PLAN-003" },
        IsProductSme = false,
        ContractHolderPolicyIds = ["POL-001", "POL-002"],
        ValidEndorsementIds = [],
        TenantId = "tenant-123",
        DependentMembersCache = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        ExistingIndividualIds = new HashSet<string>(),
        ExistingPolicyMembers = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        MemberValidationStates = new Dictionary<string, IReadOnlyList<PoliciesV3.Domain.PolicyMembers.PolicyMember>>(),
        IndividualExistenceMap = new Dictionary<string, bool>()
    };

    private static PoliciesV3.Domain.CustomFields.PolicyMemberFieldsSchema CreateTestSchema() => new([]);

    private static ValidationError CreateValidationError(string code, string propertyPath) => new(code, propertyPath, propertyPath, new Dictionary<string, object?>());

    #endregion
}