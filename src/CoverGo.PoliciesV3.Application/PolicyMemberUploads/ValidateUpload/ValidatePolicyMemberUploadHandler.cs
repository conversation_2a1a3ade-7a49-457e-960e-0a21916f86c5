using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;

public class ValidatePolicyMemberUploadHandler(
    IPolicyMemberUploadRepository policyMemberUploadRepository,
    IPolicyMemberUploadValidationErrorExporterService validationErrorExporterService,
    ILegacyPolicyService legacyPolicyService,
    CompleteUploadValidationSpecification completeValidationSpec,
    IUsersService usersService,
    IPolicyMemberQueryService policyMemberQueryService,
    IFileProcessingService fileProcessingService,
    ILogger<ValidatePolicyMemberUploadHandler> logger,
    PolicyMemberValidationDataService validationDataService)
    : ICommandHandler<ValidatePolicyMemberUploadCommand, Result<ValidatePolicyMemberUploadResponse>>
{
    #region Main Validation Flow

    /// <summary>
    /// Validates a policy member upload by gathering required data and applying business rules.
    /// Returns validation results with any errors found during the process.
    /// </summary>
    public async Task<Result<ValidatePolicyMemberUploadResponse>> Handle(
        ValidatePolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting validation for policy {PolicyId}, upload {UploadId}",
            command.PolicyId, command.UploadId);

        try
        {
            // 1. GATHER REQUIRED INFORMATION (fails fast if not found)
            Result<(PolicyMemberUpload Upload, PolicyDto Policy)> gatherResult = await GatherRequiredInformationAsync(command, cancellationToken);
            if (gatherResult.IsFailure)
            {
                return Result<ValidatePolicyMemberUploadResponse>.Failure(gatherResult.Errors);
            }

            (PolicyMemberUpload upload, PolicyDto policy) = gatherResult.Value;

            // 2. FAIL-FAST DOMAIN STATE VALIDATION (fails immediately if invalid)
            ValidateUploadState(upload);
            validationDataService.ValidatePolicyState(policy, upload.EndorsementId);

            // 3. PROCESS FILE AND VALIDATE (main workflow with guaranteed valid contexts)
            ValidationOrchestrationResult validationResults = await ProcessFileAndValidateAsync(upload, policy, cancellationToken);

            // 4. COMPLETE VALIDATION (domain and infrastructure operations)
            await CompleteValidationAsync(upload, validationResults, cancellationToken);

            logger.LogInformation("Successfully completed validation for policy {PolicyId}, upload {UploadId}",
                policy.Id, upload.Id);

            return Result<ValidatePolicyMemberUploadResponse>.Success(
                new ValidatePolicyMemberUploadResponse { PolicyMemberUpload = upload });
        }
        catch (DomainException ex)
        {
            logger.LogWarning(ex, "Domain validation failed for upload {UploadId}: {ErrorCode}", command.UploadId, ex.Code);
            return Result<ValidatePolicyMemberUploadResponse>.Failure(
                Errors.DomainException(ex.Code, ValidationConstants.PropertyPaths.Policy, ValidationConstants.Labels.Policy, ex.Message));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during validation for upload {UploadId}", command.UploadId);

            if (ex.Data.Contains(ValidationConstants.ExceptionDataKeys.Upload) && ex.Data[ValidationConstants.ExceptionDataKeys.Upload] is PolicyMemberUpload failedUpload) await MarkValidationAsFailedAsync(failedUpload, ex.Message, cancellationToken);

            throw;
        }
    }

    #endregion

    #region Information Gathering

    /// <summary>
    /// Gathers the upload and policy information needed for validation.
    /// </summary>
    private async Task<Result<(PolicyMemberUpload Upload, PolicyDto Policy)>> GatherRequiredInformationAsync(
        ValidatePolicyMemberUploadCommand command,
        CancellationToken cancellationToken)
    {
        Task<Result<PolicyMemberUpload>> findUploadTask = FindUploadByIdAsync(command.UploadId, cancellationToken);
        Task<Result<PolicyDto>> findPolicyTask = FindPolicyByIdAsync(command.PolicyId.Value.ToString(), cancellationToken);

        await Task.WhenAll(findUploadTask, findPolicyTask);

        Result<PolicyMemberUpload> uploadResult = await findUploadTask;
        Result<PolicyDto> policyResult = await findPolicyTask;

        if (uploadResult.IsFailure)
            return Result<(PolicyMemberUpload Upload, PolicyDto Policy)>.Failure(uploadResult.Errors);

        if (policyResult.IsFailure)
            return Result<(PolicyMemberUpload Upload, PolicyDto Policy)>.Failure(policyResult.Errors);

        return Result<(PolicyMemberUpload Upload, PolicyDto Policy)>.Success((uploadResult.Value, policyResult.Value));
    }

    private async Task<Result<PolicyMemberUpload>> FindUploadByIdAsync(PolicyMemberUploadId uploadId, CancellationToken cancellationToken)
    {
        try
        {
            PolicyMemberUpload? upload = await policyMemberUploadRepository.FindByIdAsync(uploadId, cancellationToken);

            return upload != null
                ? Result<PolicyMemberUpload>.Success(upload)
                : Result<PolicyMemberUpload>.Failure(Errors.PolicyMemberUploadNotFound("uploadId", uploadId.Value.ToString(), "Upload"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to find upload with ID {UploadId}", uploadId);
            throw;
        }
    }

    private async Task<Result<PolicyDto>> FindPolicyByIdAsync(string policyId, CancellationToken cancellationToken)
    {
        try
        {
            PolicyDto? policy = await legacyPolicyService.GetPolicyById(policyId, cancellationToken);
            return policy?.Id != null
                ? Result<PolicyDto>.Success(policy)
                : Result<PolicyDto>.Failure(Errors.NotFound(ValidationConstants.PropertyPaths.Policy, policyId, ValidationConstants.Labels.Policy));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to find policy with ID {PolicyId}", policyId);
            throw;
        }
    }

    #endregion

    #region Domain Validation

    /// <summary>
    /// Validates that the upload is in the correct state for validation.
    /// </summary>
    private void ValidateUploadState(PolicyMemberUpload upload)
    {
        try
        {
            upload.EnsureCanValidate();
        }
        catch (InvalidPolicyMemberUploadStatusException ex)
        {
            logger.LogWarning(ex, "Upload state validation failed for upload {UploadId}", upload.Id);
            throw new InvalidPolicyMemberUploadStatusException(
                upload.Id.ToString(),
                upload.Status.Value,
                [
                    PolicyMemberUploadStatus.REGISTERED.Value,
                    PolicyMemberUploadStatus.VALIDATING_ERROR.Value,
                    PolicyMemberUploadStatus.VALIDATED.Value,
                    PolicyMemberUploadStatus.FAILED.Value
                ]);
        }
    }



    #endregion

    #region File Processing and Validation Workflow

    /// <summary>
    /// Processes the upload file and executes the complete validation workflow.
    /// </summary>
    private async Task<ValidationOrchestrationResult> ProcessFileAndValidateAsync(
        PolicyMemberUpload upload,
        PolicyDto policy,
        CancellationToken cancellationToken)
    {
        try
        {
            // 1. MARK AS IN PROGRESS (infrastructure operation)
            await MarkUploadAsInProgressAsync(upload, cancellationToken);

            // 2. GATHER ALL VALIDATION DATA (parallel data gathering)
            (ResolvedValidationData resolvedData, FileProcessingResult fileResult, PolicyMemberFieldsSchema schema, MembersUploadFields memberData) = await GatherValidationDataAsync(policy, upload, cancellationToken);

            // 3. EARLY EXIT FOR FILE PROCESSING FAILURES
            if (!fileResult.IsSuccess)
                throw new BadFileContentException(ErrorCodes.FileProcessingFailed,
                    ErrorMessages.UploadFileProcessingError(policy.Id, upload.Path, fileResult.ErrorMessage ?? "Unknown error"));

            // 4. EARLY EXIT FOR EMPTY UPLOADS (valid but no work to do)
            if (memberData.IsEmpty)
            {
                logger.LogInformation("Upload file contains no member data - validation successful with 0 members");
                return ValidationOrchestrationResult.Success(0, 0, []);
            }

            // 5. CREATE VALIDATION CONTEXT AND EXECUTE (guaranteed valid context)
            var validationContext = CompleteValidationContext.CreateWithProcessedData(
                upload, policy, resolvedData, fileResult, schema, memberData);

            return await completeValidationSpec.ProcessUploadComplianceAsync(validationContext, cancellationToken);
        }
        catch (Exception ex) when (!(ex is BadFileContentException || ex is DomainException))
        {
            ex.Data[ValidationConstants.ExceptionDataKeys.Upload] = upload;
            throw;
        }
    }

    /// <summary>
    /// Marks the upload as in progress and ensures it's not locked by another process.
    /// </summary>
    private async Task MarkUploadAsInProgressAsync(PolicyMemberUpload upload, CancellationToken cancellationToken)
    {
        upload.MarkAsInProgress();

        bool wasSuccessful = await policyMemberUploadRepository.StartValidationIfNotLockedAsync(upload.Id, cancellationToken);
        if (!wasSuccessful) throw new UploadValidationLockedException(upload.Id.ToString());

        logger.LogInformation("Started validation for upload {UploadId}", upload.Id);
    }

    #endregion

    #region Data Gathering for Validation

    /// <summary>
    /// Gathers all the data needed for validation rules to run efficiently.
    /// This includes feature flags, product information, file contents, and member data.
    /// </summary>
    private async Task<(ResolvedValidationData ResolvedData, FileProcessingResult FileResult, PolicyMemberFieldsSchema Schema, MembersUploadFields MemberData)> GatherValidationDataAsync(
        PolicyDto policy,
        PolicyMemberUpload upload,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogDebug("Gathering validation data for policy {PolicyId}", policy.Id);

            // 1. GATHER FEATURE FLAGS AND PRODUCT DATA
            (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) =
                await validationDataService.GatherFeatureFlagsAndProductDataAsync(policy, cancellationToken);

            // 2. PROCESS FILE AND GET SCHEMA
            FileProcessingResult fileProcessingResult = await fileProcessingService.ProcessUploadFileAsync(policy.Id, upload.Path, cancellationToken);
            PolicyMemberFieldsSchema schema = await validationDataService.GetDataSchemaForUploadAsync(policy, upload.EndorsementId, cancellationToken);

            // 3. PROCESS MEMBER DATA WITH STRICT VALIDATION
            MembersUploadFields memberData;
            var memberDataResults = MemberDataResults.Empty();
            if (fileProcessingResult.IsSuccess && fileProcessingResult.MemberData?.Any() == true)
            {
                MembersUploadFields transformedData = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(fileProcessingResult.MemberData, schema);
                memberData = MembersUploadFields.CreateNonEmpty([.. transformedData.AsReadOnlyList()]);
                memberDataResults = await validationDataService.GatherMemberSpecificDataAsync(memberData, policy, upload.EndorsementId, usersService, policyMemberQueryService, cancellationToken);
            }
            else
                memberData = MembersUploadFields.Empty();

            // 4. CREATE RESOLVED VALIDATION DATA
            List<EndorsementId> contractHolderScopeEndorsements = await validationDataService.GetContractHolderScopeEndorsementsAsync(
                contractHolderPolicies ?? [], policy.IsV2, cancellationToken);

            ResolvedValidationData resolvedData = validationDataService.CreateResolvedValidationData(
                policy,
                featureFlags,
                availablePlans,
                packageType,
                contractHolderPolicies,
                contractHolderScopeEndorsements,
                memberDataResults,
                policy.GetValidEndorsementIds(upload.EndorsementId?.Value.ToString()).Where(id => !string.IsNullOrEmpty(id)).Cast<string>().ToList(),
                memberDataResults.DependentMembersCache);

            return (resolvedData, fileProcessingResult, schema, memberData);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error gathering validation data for policy {PolicyId}", policy.Id);
            throw;
        }
    }


    #endregion

    #region Validation Completion

    /// <summary>
    /// Completes the validation process by processing results and updating the upload status.
    /// </summary>
    private async Task CompleteValidationAsync(
        PolicyMemberUpload upload,
        ValidationOrchestrationResult results,
        CancellationToken cancellationToken)
    {
        // 1. PROCESS VALIDATION RESULTS (domain operation)
        upload.ProcessValidationResults(results);

        // 2. PERSIST VALIDATION ERRORS (only if there are errors)
        if (upload.ValidationErrors.Count > 0)
        {
            await validationErrorExporterService.PersistValidationErrorsAsync(upload.ValidationErrors, cancellationToken);
            logger.LogInformation("Persisted {ErrorCount} validation errors for upload {UploadId}",
                upload.ValidationErrors.Count, upload.Id);
        }

        // 3. MARK AS COMPLETED (domain operation)
        upload.MarkAsCompleted();

        // 4. SAVE TO REPOSITORY (infrastructure operation)
        if (upload.CanCompleteValidation())
        {
            bool wasCompleted = await policyMemberUploadRepository.CompleteValidationIfNotLockedAsync(
                upload.Id, upload.Status, upload.ValidMembersCount ?? 0, upload.InvalidMembersCount ?? 0,
                cancellationToken);

            if (wasCompleted)
                logger.LogInformation("Successfully completed validation for upload {UploadId} with status {Status}. Valid: {ValidCount}, Invalid: {InvalidCount}",
                    upload.Id, upload.Status.Value, upload.ValidMembersCount, upload.InvalidMembersCount);
            else
                logger.LogWarning("Could not complete validation for upload {UploadId} - it may have been modified by another process", upload.Id);
        }
    }

    /// <summary>
    /// Handles unexpected validation failures by updating the upload status.
    /// </summary>
    private async Task MarkValidationAsFailedAsync(PolicyMemberUpload upload, string errorMessage, CancellationToken cancellationToken)
    {
        upload.FailUpload();

        bool wasSuccessful = await policyMemberUploadRepository.FailValidationIfNotLockedAsync(upload.Id, errorMessage, cancellationToken);
        if (wasSuccessful)
            logger.LogInformation("Marked upload {UploadId} as failed: {ErrorMessage}", upload.Id, errorMessage);
        else
            logger.LogWarning("Could not mark upload {UploadId} as failed - it may have been modified by another process", upload.Id);
    }

    #endregion
}
