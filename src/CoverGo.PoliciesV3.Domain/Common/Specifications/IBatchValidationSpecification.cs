namespace CoverGo.PoliciesV3.Domain.Common.Specifications;

public interface IBatchValidationSpecification<T> : ISpecification<T>
{
    /// <summary>
    /// Performs comprehensive batch validation and returns rich validation results.
    /// This method provides detailed validation outcomes including:
    /// - Item-specific validation errors with row indexing
    /// - Global validation errors that apply to the entire batch
    /// - Summary statistics (total, valid, invalid counts)
    /// - Success/failure status with comprehensive error details
    /// </summary>
    /// <param name="context">The validation context containing the batch data to validate</param>
    /// <param name="cancellationToken">Cancellation token for the async operation</param>
    /// <returns>
    /// A BatchValidationResult containing:
    /// - Overall success/failure status
    /// - Detailed error tracking by item index
    /// - Global errors affecting the entire batch
    /// - Summary statistics for reporting
    /// </returns>
    Task<BatchValidationResult> EvaluateBusinessRulesAsync(
        T context,
        CancellationToken cancellationToken = default);
}
