using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;

public class CompleteUploadValidationSpecification(
    UploadWideValidationSpecification uploadWideSpec,
    IndividualMemberValidationSpecification individualMemberSpec,
    IValidationErrorAggregator errorAggregator,
    ILogger<CompleteUploadValidationSpecification> logger) : CompositeSpecificationBase<CompleteValidationContext>
{
    #region Specification Properties

    public override string BusinessRuleName => "Complete Upload Validation";
    public override string Description => "Validates entire policy member upload including file processing, upload-wide rules, and individual member validation";

    #endregion

    #region Main Validation Logic

    public override async Task<BatchValidationResult> ValidateBatchAsync(
        CompleteValidationContext context,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            // 1. VALIDATE CONTEXT AND PREPARE DATA (guaranteed to succeed or throw)
            ValidatedData validatedData = ValidateContextAndPrepareData(context);

            logger.LogInformation("Processing {MemberCount} members for validation", validatedData.TransformedData.Count);

            // 2. EARLY EXIT FOR EMPTY DATA
            if (validatedData.TransformedData.IsEmpty)
            {
                logger.LogInformation("No members to validate - returning success");
                return BatchValidationResult.Success(0);
            }

            // 3. RUN PARALLEL VALIDATIONS
            List<BatchValidationResult> validationResults = await RunValidationsAsync(context, validatedData, cancellationToken);

            // 4. AGGREGATE RESULTS
            BatchValidationResult finalResult = errorAggregator.AggregateResults(validationResults, validatedData.TransformedData.Count);

            logger.LogInformation("Validation completed: {ValidCount} valid, {InvalidCount} invalid members",
                finalResult.ValidCount, finalResult.InvalidCount);

            return finalResult;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in complete upload validation for policy {PolicyId}, upload {UploadId}",
                context.Policy.Id, context.Upload.Id);
            throw;
        }
    }

    public virtual async Task<ValidationOrchestrationResult> ValidateAsync(
        CompleteValidationContext context,
        CancellationToken cancellationToken = default)
    {
        BatchValidationResult batchResult = await ValidateBatchAsync(context, cancellationToken);
        return ValidationOrchestrationResult.Success(
            batchResult.ValidCount,
            batchResult.InvalidCount,
            batchResult.RowErrors);
    }

    #endregion

    #region Protected Virtual Methods

    protected virtual ValidatedData ValidateContextAndPrepareData(CompleteValidationContext context)
    {
        // Validate context state
        context.ValidateForProcessing();

        logger.LogInformation("Starting complete upload validation for policy {PolicyId}, upload {UploadId}",
            context.Policy.Id, context.Upload.Id);

        // Get processed data (throws if not available)
        (FileProcessingResult fileResult, PolicyMemberFieldsSchema schema, MembersUploadFields transformedData) = context.GetProcessedDataOrThrow();

        // Validate file processing results
        if (!fileResult.IsSuccess)
        {
            throw new BadFileContentException(ErrorCodes.FileProcessingFailed,
                ErrorMessages.UploadFileProcessingError(context.Policy.Id, context.Upload.Path,
                    fileResult.ErrorMessage ?? "File processing failed"));
        }

        return new ValidatedData(fileResult, schema, transformedData, context.ResolvedData);
    }

    protected virtual async Task<List<BatchValidationResult>> RunValidationsAsync(
        CompleteValidationContext context,
        ValidatedData validatedData,
        CancellationToken cancellationToken)
    {
        Task<BatchValidationResult> uploadWideTask = RunUploadWideValidationAsync(context, validatedData, cancellationToken);
        Task<BatchValidationResult> memberValidationTask = RunIndividualMemberValidationAsync(context, validatedData, cancellationToken);

        logger.LogDebug("Executing upload-wide and member validations in parallel");

        BatchValidationResult[] results = await Task.WhenAll(uploadWideTask, memberValidationTask);

        logger.LogDebug("Parallel validations completed - Upload-wide: {UploadValid}/{UploadInvalid}, Members: {MemberValid}/{MemberInvalid}",
            results[0].ValidCount, results[0].InvalidCount,
            results[1].ValidCount, results[1].InvalidCount);

        return [.. results];
    }

    protected virtual async Task<BatchValidationResult> RunUploadWideValidationAsync(
        CompleteValidationContext context,
        ValidatedData validatedData,
        CancellationToken cancellationToken)
    {
        var uploadWideContext = UploadWideValidationContext.Create(
            context.Upload, context.Policy, validatedData.TransformedData,
            validatedData.Schema, validatedData.ResolvedData);

        return await uploadWideSpec.ValidateBatchAsync(uploadWideContext, cancellationToken);
    }

    protected virtual async Task<BatchValidationResult> RunIndividualMemberValidationAsync(
        CompleteValidationContext context,
        ValidatedData validatedData,
        CancellationToken cancellationToken)
    {
        var memberContext = IndividualMemberValidationContext.Create(
            context.Policy, validatedData.TransformedData,
            validatedData.Schema, validatedData.ResolvedData, context.Upload.EndorsementId);

        return await individualMemberSpec.ValidateBatchAsync(memberContext, cancellationToken);
    }

    #endregion
}

/// <summary>
/// Container for validated data with guaranteed non-null properties
/// </summary>
public sealed record ValidatedData(
    FileProcessingResult FileResult,
    PolicyMemberFieldsSchema Schema,
    MembersUploadFields TransformedData,
    ResolvedValidationData ResolvedData);

#region Supporting Classes

public interface IValidationErrorAggregator
{
    BatchValidationResult AggregateResults(List<BatchValidationResult> results, int totalMemberCount);
}

public class ValidationErrorAggregator(ILogger<ValidationErrorAggregator> logger) : IValidationErrorAggregator
{
    public BatchValidationResult AggregateResults(List<BatchValidationResult> results, int totalMemberCount)
    {
        logger.LogDebug("Aggregating {ResultCount} validation results for {TotalMembers} members",
            results.Count, totalMemberCount);

        Dictionary<int, List<ValidationError>>[] errorDictionaries = [.. results.Select(r => r.RowErrors)];
        Dictionary<int, List<ValidationError>> allRowErrors = SpecificationErrorAggregator.MergeRowIndexedErrors(errorDictionaries);

        int invalidCount = allRowErrors.Count;
        int validCount = totalMemberCount - invalidCount;

        BatchValidationResult finalResult = invalidCount > 0
            ? BatchValidationResult.WithErrors(validCount, invalidCount, allRowErrors)
            : BatchValidationResult.Success(validCount);

        logger.LogDebug("Aggregated result - Valid: {ValidCount}, Invalid: {InvalidCount}",
            validCount, invalidCount);

        return finalResult;
    }
}

#endregion